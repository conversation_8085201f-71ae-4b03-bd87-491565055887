/**
 * Appearance Settings Service
 * Handles appearance customization with database and Cloudinary integration
 */

import { supabase } from './supabase'
import { logger } from './logger'
import { AppearanceSync } from './appearance-sync'

export interface AppearanceSettings {
  backgroundType: 'gradient' | 'photo'
  gradientStyle: string
  customColors: {
    primary: string
    secondary: string
  }
  backgroundImage?: string | null
  backgroundImagePublicId?: string | null
}

export class AppearanceService {
  private static readonly SETTING_KEY = 'landing_page_background'
  private static readonly STORAGE_KEY = 'appearance-settings-fallback'

  /**
   * Get current appearance settings with database-first approach
   */
  static async getSettings(): Promise<AppearanceSettings> {
    try {
      // Try database first for consistent settings across all ports
      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value')
        .eq('setting_key', this.SETTING_KEY)
        .single()

      if (!error && data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings
        logger.info('✅ Loaded settings from database (consistent across ports)')
        // Update localStorage backup
        this.saveLocalStorageSettings(settings)
        return settings
      }

      logger.warn('Database query failed or no data found', { error: error?.message })

      // Database failed, try localStorage as fallback
      const localSettings = this.getLocalStorageSettings()
      if (localSettings) {
        logger.info('📱 Using localStorage fallback (port-specific)')
        // Try to sync localStorage to database for consistency
        await this.syncToDatabase(localSettings)
        return localSettings
      }

      // No settings anywhere, use defaults and save to database
      logger.info('🎨 Using default settings and saving to database')
      const defaults = this.getDefaultSettings()
      await this.syncToDatabase(defaults)
      this.saveLocalStorageSettings(defaults)
      return defaults

    } catch (error) {
      logger.warn('Database error, using localStorage fallback', { error: (error as Error).message })

      // Use localStorage if available
      const localSettings = this.getLocalStorageSettings()
      if (localSettings) {
        logger.info('📱 Using localStorage fallback (database error)')
        return localSettings
      }

      // Final fallback to defaults
      logger.info('🎨 Using default settings (final fallback)')
      const defaults = this.getDefaultSettings()
      this.saveLocalStorageSettings(defaults)
      return defaults
    }
  }

  /**
   * Sync settings to database for consistency across ports
   */
  private static async syncToDatabase(settings: AppearanceSettings): Promise<void> {
    try {
      const { error } = await supabase
        .from('appearance_settings')
        .upsert({
          setting_key: this.SETTING_KEY,
          setting_value: settings
        })

      if (!error) {
        logger.info('🔄 Settings synced to database for port consistency')
      }
    } catch (error) {
      logger.warn('Failed to sync to database', { error: (error as Error).message })
    }
  }

  /**
   * Get settings from localStorage
   */
  private static getLocalStorageSettings(): AppearanceSettings | null {
    try {
      if (typeof window === 'undefined') return null

      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        return JSON.parse(stored) as AppearanceSettings
      }
    } catch (error) {
      logger.warn('Failed to read localStorage', { error })
    }
    return null
  }

  /**
   * Save settings to localStorage
   */
  private static saveLocalStorageSettings(settings: AppearanceSettings): void {
    try {
      if (typeof window === 'undefined') return
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(settings))
    } catch (error) {
      logger.warn('Failed to save to localStorage', { error })
    }
  }



  /**
   * Save appearance settings with robust fallback and real-time sync
   */
  static async saveSettings(settings: AppearanceSettings): Promise<boolean> {
    // Always save to localStorage first for immediate persistence
    this.saveLocalStorageSettings(settings)

    try {
      // Try to save to database
      const { error } = await supabase
        .from('appearance_settings')
        .upsert({
          setting_key: this.SETTING_KEY,
          setting_value: settings
        })

      if (error) {
        logger.warn('💾 Database save failed, using localStorage only', { error: error.message })
        // Still broadcast the change for local sync
        AppearanceSync.broadcastChange(settings)
        return true // Still successful since localStorage worked
      }

      logger.info('✅ Settings saved to database and localStorage', {
        backgroundType: settings.backgroundType,
        hasImage: !!settings.backgroundImage
      })

      // Broadcast change for real-time sync (database change will also trigger sync)
      AppearanceSync.broadcastChange(settings)
      return true

    } catch (error) {
      logger.warn('💾 Database error, using localStorage only', { error: (error as Error).message })
      // Still broadcast the change for local sync
      AppearanceSync.broadcastChange(settings)
      return true // Still successful since localStorage worked
    }
  }

  /**
   * Force refresh settings from database (for troubleshooting)
   */
  static async forceRefreshFromDatabase(): Promise<AppearanceSettings | null> {
    try {
      logger.info('🔄 Force refreshing settings from database')

      const { data, error } = await supabase
        .from('appearance_settings')
        .select('setting_value')
        .eq('setting_key', this.SETTING_KEY)
        .single()

      if (error) {
        logger.error('Failed to refresh from database', { error: error.message })
        return null
      }

      if (data?.setting_value) {
        const settings = data.setting_value as AppearanceSettings
        logger.info('✅ Settings refreshed from database', {
          backgroundType: settings.backgroundType,
          hasImage: !!settings.backgroundImage
        })

        // Update localStorage and broadcast
        this.saveLocalStorageSettings(settings)
        AppearanceSync.broadcastChange(settings)
        return settings
      }

      return null
    } catch (error) {
      logger.error('Error during force refresh', { error })
      return null
    }
  }

  /**
   * Upload background image to Cloudinary
   */
  static async uploadBackgroundImage(file: File): Promise<{
    success: boolean
    imageUrl?: string
    publicId?: string
    error?: string
  }> {
    try {
      const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
      const uploadPreset = process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET

      if (!cloudName) {
        throw new Error('Cloudinary cloud name not configured')
      }

      logger.info('Starting Cloudinary upload', {
        cloudName,
        uploadPreset,
        fileName: file.name,
        fileSize: file.size
      })

      const formData = new FormData()
      formData.append('file', file)

      // Try multiple upload presets as fallback
      const presets = [
        uploadPreset || 'revantad-backgrounds',
        'sari-sari-products', // Fallback to existing preset
        'ml_default' // Cloudinary default preset
      ]

      let lastError: Error | null = null

      for (const preset of presets) {
        try {
          logger.info(`Trying upload preset: ${preset}`)

          const uploadFormData = new FormData()
          uploadFormData.append('file', file)
          uploadFormData.append('upload_preset', preset)
          uploadFormData.append('folder', 'revantad/backgrounds')

          const response = await fetch(
            `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
            {
              method: 'POST',
              body: uploadFormData
            }
          )

          if (!response.ok) {
            const errorText = await response.text()
            logger.warn(`Upload preset ${preset} failed: ${response.status} ${response.statusText}`, { errorText })
            lastError = new Error(`Upload failed with preset ${preset}: ${response.statusText}`)
            continue
          }

          const result = await response.json()

          logger.info('Background image uploaded successfully', {
            preset: preset,
            publicId: result.public_id,
            url: result.secure_url
          })

          return {
            success: true,
            imageUrl: result.secure_url,
            publicId: result.public_id
          }
        } catch (error) {
          logger.warn(`Upload attempt with preset ${preset} failed`, { error })
          lastError = error as Error
          continue
        }
      }

      // If all presets failed, throw the last error
      throw lastError || new Error('All upload presets failed')

    } catch (error) {
      logger.error('Error uploading background image', { error })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Delete background image from Cloudinary
   */
  static async deleteBackgroundImage(publicId: string): Promise<boolean> {
    try {
      // Note: For security, image deletion should be handled by a backend API
      // This is a placeholder for the frontend implementation
      logger.info('Background image deletion requested', { publicId })
      
      // In a real implementation, you would call your backend API here
      // which would handle the Cloudinary deletion securely
      
      return true
    } catch (error) {
      logger.error('Error deleting background image', { error, publicId })
      return false
    }
  }

  /**
   * Update background image settings
   */
  static async updateBackgroundImage(
    imageUrl: string,
    publicId: string
  ): Promise<boolean> {
    try {
      const currentSettings = await this.getSettings()
      
      // Delete old image if exists
      if (currentSettings.backgroundImagePublicId) {
        await this.deleteBackgroundImage(currentSettings.backgroundImagePublicId)
      }

      const updatedSettings: AppearanceSettings = {
        ...currentSettings,
        backgroundType: 'photo',
        backgroundImage: imageUrl,
        backgroundImagePublicId: publicId
      }

      return await this.saveSettings(updatedSettings)
    } catch (error) {
      logger.error('Error updating background image settings', { error })
      return false
    }
  }

  /**
   * Remove background image
   */
  static async removeBackgroundImage(): Promise<boolean> {
    try {
      const currentSettings = await this.getSettings()
      
      // Delete image from Cloudinary if exists
      if (currentSettings.backgroundImagePublicId) {
        await this.deleteBackgroundImage(currentSettings.backgroundImagePublicId)
      }

      const updatedSettings: AppearanceSettings = {
        ...currentSettings,
        backgroundType: 'gradient',
        backgroundImage: null,
        backgroundImagePublicId: null
      }

      return await this.saveSettings(updatedSettings)
    } catch (error) {
      logger.error('Error removing background image', { error })
      return false
    }
  }

  /**
   * Get default appearance settings
   */
  private static getDefaultSettings(): AppearanceSettings {
    return {
      backgroundType: 'gradient',
      gradientStyle: 'hero-gradient',
      customColors: {
        primary: '#22c55e',
        secondary: '#facc15'
      },
      backgroundImage: null,
      backgroundImagePublicId: null
    }
  }
}
